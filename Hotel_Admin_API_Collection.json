{"info": {"_postman_id": "hotel-admin-api-collection", "name": "Hotel Admin API Collection", "description": "Collection để test các API Admin của Hotel Booking System", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Authentication", "item": [{"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"123456\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/login", "host": ["{{base_url}}"], "path": ["api", "auth", "login"]}}}, {"name": "Refresh <PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"refreshToken\": \"{{refresh_token}}\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/refresh", "host": ["{{base_url}}"], "path": ["api", "auth", "refresh"]}}}]}, {"name": "Admin Dashboard", "item": [{"name": "Get Dashboard Data", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/admin/dashboard", "host": ["{{base_url}}"], "path": ["api", "admin", "dashboard"]}}}]}, {"name": "Admin Booking Management", "item": [{"name": "Get All Bookings", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/admin/bookings?page=0&size=10", "host": ["{{base_url}}"], "path": ["api", "admin", "bookings"], "query": [{"key": "page", "value": "0"}, {"key": "size", "value": "10"}]}}}, {"name": "Get Booking Detail", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/admin/bookings/1", "host": ["{{base_url}}"], "path": ["api", "admin", "bookings", "1"]}}}, {"name": "Update Booking Status", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"CONFIRMED\",\n  \"note\": \"Booking confirmed by admin\"\n}"}, "url": {"raw": "{{base_url}}/api/admin/bookings/1/status", "host": ["{{base_url}}"], "path": ["api", "admin", "bookings", "1", "status"]}}}, {"name": "Confirm Booking", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/admin/bookings/1/confirm", "host": ["{{base_url}}"], "path": ["api", "admin", "bookings", "1", "confirm"]}}}, {"name": "Check-in", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/admin/bookings/1/check-in", "host": ["{{base_url}}"], "path": ["api", "admin", "bookings", "1", "check-in"]}}}, {"name": "Check-out", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/admin/bookings/1/check-out", "host": ["{{base_url}}"], "path": ["api", "admin", "bookings", "1", "check-out"]}}}, {"name": "Cancel Booking", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/admin/bookings/1/cancel?reason=Customer request", "host": ["{{base_url}}"], "path": ["api", "admin", "bookings", "1", "cancel"], "query": [{"key": "reason", "value": "Customer request"}]}}}, {"name": "Get Booking Statistics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/admin/bookings/statistics?fromDate=2024-01-01&toDate=2024-01-31", "host": ["{{base_url}}"], "path": ["api", "admin", "bookings", "statistics"], "query": [{"key": "fromDate", "value": "2024-01-01"}, {"key": "toDate", "value": "2024-01-31"}]}}}, {"name": "Get Revenue Report", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/admin/bookings/revenue/by-hotel?fromDate=2024-01-01&toDate=2024-01-31", "host": ["{{base_url}}"], "path": ["api", "admin", "bookings", "revenue", "by-hotel"], "query": [{"key": "fromDate", "value": "2024-01-01"}, {"key": "toDate", "value": "2024-01-31"}]}}}, {"name": "Get Upcoming Check-ins", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/admin/bookings/upcoming-checkins", "host": ["{{base_url}}"], "path": ["api", "admin", "bookings", "upcoming-checkins"]}}}, {"name": "<PERSON> No-Show", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/admin/bookings/mark-no-show", "host": ["{{base_url}}"], "path": ["api", "admin", "bookings", "mark-no-show"]}}}, {"name": "Export Bookings", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/admin/bookings/export?status=CONFIRMED", "host": ["{{base_url}}"], "path": ["api", "admin", "bookings", "export"], "query": [{"key": "status", "value": "CONFIRMED"}]}}}]}, {"name": "Hotel Management", "item": [{"name": "Get All Hotels", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/hotels", "host": ["{{base_url}}"], "path": ["api", "hotels"]}}}, {"name": "Get Hotel Detail", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/hotels/1", "host": ["{{base_url}}"], "path": ["api", "hotels", "1"]}}}, {"name": "Create Hotel", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"hotelName\": \"New Luxury Hotel\",\n  \"location\": \"789 Le Loi, District 1, Ho Chi Minh City\",\n  \"description\": \"Brand new 5-star luxury hotel in the heart of Saigon\"\n}"}, "url": {"raw": "{{base_url}}/api/hotels", "host": ["{{base_url}}"], "path": ["api", "hotels"]}}}, {"name": "Update Hotel", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"hotelName\": \"Grand Hotel Saigon - Updated\",\n  \"location\": \"123 Nguyen Hue, District 1, Ho Chi Minh City\",\n  \"description\": \"Updated description for Grand Hotel Saigon\"\n}"}, "url": {"raw": "{{base_url}}/api/hotels/1", "host": ["{{base_url}}"], "path": ["api", "hotels", "1"]}}}, {"name": "Delete Hotel", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/hotels/1", "host": ["{{base_url}}"], "path": ["api", "hotels", "1"]}}}]}, {"name": "Room Management", "item": [{"name": "Get All Rooms", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/rooms", "host": ["{{base_url}}"], "path": ["api", "rooms"]}}}, {"name": "Get Room Detail", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/rooms/1", "host": ["{{base_url}}"], "path": ["api", "rooms", "1"]}}}, {"name": "Create Room", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"roomClassId\": 1,\n  \"roomNumber\": \"106\",\n  \"roomStatus\": \"AVAILABLE\"\n}"}, "url": {"raw": "{{base_url}}/api/rooms", "host": ["{{base_url}}"], "path": ["api", "rooms"]}}}, {"name": "Update Room", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"roomNumber\": \"101\",\n  \"roomStatus\": \"MAINTENANCE\"\n}"}, "url": {"raw": "{{base_url}}/api/rooms/1", "host": ["{{base_url}}"], "path": ["api", "rooms", "1"]}}}, {"name": "Delete Room", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/rooms/1", "host": ["{{base_url}}"], "path": ["api", "rooms", "1"]}}}]}, {"name": "Room Class Management", "item": [{"name": "Get All Room Classes", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/room-classes", "host": ["{{base_url}}"], "path": ["api", "room-classes"]}}}, {"name": "Get Room Class Detail", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/room-classes/1", "host": ["{{base_url}}"], "path": ["api", "room-classes", "1"]}}}, {"name": "Create Room Class", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"roomClassName\": \"Premium Suite\",\n  \"quantity\": \"5\",\n  \"priceOriginal\": 5000000,\n  \"description\": \"Ultra luxury suite with premium amenities\",\n  \"discountPercent\": 0,\n  \"capacity\": 2,\n  \"hotelId\": 1\n}"}, "url": {"raw": "{{base_url}}/api/room-classes", "host": ["{{base_url}}"], "path": ["api", "room-classes"]}}}]}, {"name": "User Management", "item": [{"name": "Get All Users", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/users", "host": ["{{base_url}}"], "path": ["api", "users"]}}}, {"name": "Get User Detail", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/users/1", "host": ["{{base_url}}"], "path": ["api", "users", "1"]}}}, {"name": "Create User", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"123456\",\n  \"firstName\": \"New\",\n  \"lastName\": \"User\",\n  \"phoneNo\": \"0555666777\",\n  \"roles\": [\"USER\"]\n}"}, "url": {"raw": "{{base_url}}/api/users", "host": ["{{base_url}}"], "path": ["api", "users"]}}}]}], "variable": [{"key": "base_url", "value": "http://localhost:8080", "type": "string"}, {"key": "access_token", "value": "your_jwt_token_here", "type": "string"}, {"key": "refresh_token", "value": "your_refresh_token_here", "type": "string"}]}