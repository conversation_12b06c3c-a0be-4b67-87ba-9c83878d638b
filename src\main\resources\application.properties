spring.application.name=identity-service

spring.datasource.url=*****************************************
spring.datasource.username=root
spring.datasource.password=123456
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL8Dialect

spring.jpa.properties.hibernate.format_sql=true

jwt.signerKey=u+h0XELZz8txv/9muTpovHkpw7w0bA4lAqATGyy9tgsoIHW2CSzXpz5PW4IPhcay

cloudinary.api-key=487623424173991
cloudinary.api-secret=eefszk7NzS8j6cNEhYJkmErqtrY
cloudinary.cloud-name=dv3uxd3ad
cloudinary.secure=true

spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

spring.security.oauth2.client.registration.google.client-id=${GOOGLE_CLIENT_ID}
spring.security.oauth2.client.registration.google.client-secret=${GOOGLE_CLIENT_SECRET}
spring.security.oauth2.client.registration.google.redirect-uri=${GOOGLE_REDIRECT_URI}
spring.security.oauth2.client.registration.google.scope=openid,email,profile

spring.security.oauth2.client.provider.google.authorization-uri=https://accounts.google.com/o/oauth2/v2/auth
spring.security.oauth2.client.provider.google.token-uri=https://oauth2.googleapis.com/token
spring.security.oauth2.client.provider.google.user-info-uri=https://www.googleapis.com/oauth2/v3/userinfo
spring.security.oauth2.client.provider.google.user-name-attribute=sub

logging.level.org.springframework.security: DEBUG
logging.level.org.springframework.web.client.RestTemplate: DEBUG

# Additional session configuration
server.servlet.session.timeout=30m
server.servlet.session.cookie.http-only=true
server.servlet.session.cookie.secure=false
server.servlet.session.cookie.same-site=lax

# Debug OAuth2 in detail
logging.level.org.springframework.security.oauth2=TRACE
logging.level.org.springframework.security.oauth2.client=TRACE
spring.redis.host=localhost
spring.redis.port=6379
