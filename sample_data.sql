-- Sample Data cho Hotel Booking System
-- Chạy script này sau khi khởi động ứng dụng để có dữ liệu test

-- 1. Insert Roles
INSERT IGNORE INTO role (name, description) VALUES
('ADMIN', 'Administrator - Full access to system'),
('USER', 'Regular user - Basic access'),
('STAFF', 'Hotel staff - Limited admin access');

-- 2. Insert Users (password: 123456 - đã được hash bằng BCrypt)
INSERT IGNORE INTO user (email, password, first_name, last_name, phone_no, provider, provider_id, create_at, update_at) VALUES
('<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDa', 'Admin', 'User', '**********', 'LOCAL', NULL, NOW(), NOW()),
('<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDa', 'Staff', 'User', '**********', 'LOCAL', NULL, NOW(), NOW()),
('<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDa', 'John', 'Doe', '**********', 'LOCAL', NULL, NOW(), NOW()),
('<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDa', 'Jane', 'Smith', '**********', 'LOCAL', NULL, NOW(), NOW());

-- 3. Insert User-Role relationships
INSERT IGNORE INTO user_role (user_id, role_name) VALUES
(1, 'ADMIN'),
(2, 'STAFF'),
(3, 'USER'),
(4, 'USER');

-- 4. Insert Hotels
INSERT IGNORE INTO hotel (hotel_name, location, description, image_url, create_at, update_at) VALUES
('Grand Hotel Saigon', '123 Nguyen Hue, District 1, Ho Chi Minh City', 'Luxury 5-star hotel in the heart of Saigon with modern amenities and excellent service', 'https://example.com/grand-hotel-saigon.jpg', NOW(), NOW()),
('Seaside Resort Nha Trang', '456 Tran Phu, Nha Trang City', 'Beachfront resort with stunning ocean views and private beach access', 'https://example.com/seaside-resort-nhatrang.jpg', NOW(), NOW()),
('Mountain View Hotel Da Lat', '789 Tran Hung Dao, Da Lat City', 'Cozy mountain hotel with beautiful garden views and cool climate', 'https://example.com/mountain-view-dalat.jpg', NOW(), NOW());

-- 5. Insert Bed Types
INSERT IGNORE INTO bed_type (bed_name) VALUES
('Single Bed'),
('Double Bed'),
('Queen Bed'),
('King Bed'),
('Twin Beds'),
('Bunk Bed');

-- 6. Insert Features
INSERT IGNORE INTO feature (feature_name) VALUES
('WiFi'),
('Air Conditioning'),
('Balcony'),
('Ocean View'),
('Mountain View'),
('City View'),
('Mini Bar'),
('Safe'),
('Bathtub'),
('Shower');

-- 7. Insert Room Classes
INSERT IGNORE INTO room_class (room_class_name, quantity, price_original, description, discount_percent, capacity, hotel_id, create_at, update_at) VALUES
('Deluxe Single Room', '20', 1500000, 'Comfortable single room with city view', 10, 1, 1, NOW(), NOW()),
('Deluxe Double Room', '25', 2000000, 'Spacious double room with modern amenities', 15, 2, 1, NOW(), NOW()),
('Executive Suite', '10', 3500000, 'Luxury suite with separate living area', 5, 2, 1, NOW(), NOW()),
('Ocean View Room', '30', 2500000, 'Beautiful room with direct ocean views', 20, 2, 2, NOW(), NOW()),
('Garden Villa', '15', 4000000, 'Private villa surrounded by tropical gardens', 10, 4, 2, NOW(), NOW()),
('Mountain Cabin', '12', 1800000, 'Cozy cabin with mountain views', 15, 2, 3, NOW(), NOW()),
('Family Room', '8', 2800000, 'Large room perfect for families', 20, 4, 3, NOW(), NOW());

-- 8. Insert Room Class - Bed Type relationships
INSERT IGNORE INTO room_class_bed_type (room_class_id, bed_type_id, quantity) VALUES
(1, 1, 1), -- Deluxe Single Room - Single Bed
(2, 2, 1), -- Deluxe Double Room - Double Bed
(3, 4, 1), -- Executive Suite - King Bed
(4, 3, 1), -- Ocean View Room - Queen Bed
(5, 4, 1), -- Garden Villa - King Bed
(6, 2, 1), -- Mountain Cabin - Double Bed
(7, 5, 2); -- Family Room - Twin Beds

-- 9. Insert Room Class - Feature relationships
INSERT IGNORE INTO room_class_feature (room_class_id, feature_id) VALUES
(1, 1), (1, 2), (1, 6), (1, 7), (1, 8), (1, 10), -- Deluxe Single Room features
(2, 1), (2, 2), (2, 3), (2, 6), (2, 7), (2, 8), (2, 10), -- Deluxe Double Room features
(3, 1), (3, 2), (3, 3), (3, 6), (3, 7), (3, 8), (3, 9), (3, 10), -- Executive Suite features
(4, 1), (4, 2), (4, 4), (4, 6), (4, 7), (4, 8), (4, 10), -- Ocean View Room features
(5, 1), (5, 2), (5, 4), (5, 6), (5, 7), (5, 8), (5, 9), (5, 10), -- Garden Villa features
(6, 1), (6, 2), (6, 5), (6, 6), (6, 7), (6, 8), (6, 10), -- Mountain Cabin features
(7, 1), (7, 2), (7, 5), (7, 6), (7, 7), (7, 8), (7, 10); -- Family Room features

-- 10. Insert Rooms
INSERT IGNORE INTO room (room_class_id, room_status, room_number, create_at, update_at) VALUES
-- Deluxe Single Rooms (Hotel 1)
(1, 'AVAILABLE', '101', NOW(), NOW()),
(1, 'AVAILABLE', '102', NOW(), NOW()),
(1, 'AVAILABLE', '103', NOW(), NOW()),
(1, 'AVAILABLE', '104', NOW(), NOW()),
(1, 'AVAILABLE', '105', NOW(), NOW()),

-- Deluxe Double Rooms (Hotel 1)
(2, 'AVAILABLE', '201', NOW(), NOW()),
(2, 'AVAILABLE', '202', NOW(), NOW()),
(2, 'AVAILABLE', '203', NOW(), NOW()),
(2, 'AVAILABLE', '204', NOW(), NOW()),
(2, 'AVAILABLE', '205', NOW(), NOW()),

-- Executive Suites (Hotel 1)
(3, 'AVAILABLE', '301', NOW(), NOW()),
(3, 'AVAILABLE', '302', NOW(), NOW()),
(3, 'AVAILABLE', '303', NOW(), NOW()),

-- Ocean View Rooms (Hotel 2)
(4, 'AVAILABLE', 'A101', NOW(), NOW()),
(4, 'AVAILABLE', 'A102', NOW(), NOW()),
(4, 'AVAILABLE', 'A103', NOW(), NOW()),
(4, 'AVAILABLE', 'A104', NOW(), NOW()),
(4, 'AVAILABLE', 'A105', NOW(), NOW()),

-- Garden Villas (Hotel 2)
(5, 'AVAILABLE', 'V101', NOW(), NOW()),
(5, 'AVAILABLE', 'V102', NOW(), NOW()),
(5, 'AVAILABLE', 'V103', NOW(), NOW()),

-- Mountain Cabins (Hotel 3)
(6, 'AVAILABLE', 'C101', NOW(), NOW()),
(6, 'AVAILABLE', 'C102', NOW(), NOW()),
(6, 'AVAILABLE', 'C103', NOW(), NOW()),

-- Family Rooms (Hotel 3)
(7, 'AVAILABLE', 'F101', NOW(), NOW()),
(7, 'AVAILABLE', 'F102', NOW(), NOW()),
(7, 'AVAILABLE', 'F103', NOW(), NOW());

-- 11. Insert Room Images
INSERT IGNORE INTO room_image (room_class_id, path) VALUES
(1, 'https://example.com/rooms/deluxe-single-1.jpg'),
(1, 'https://example.com/rooms/deluxe-single-2.jpg'),
(2, 'https://example.com/rooms/deluxe-double-1.jpg'),
(2, 'https://example.com/rooms/deluxe-double-2.jpg'),
(3, 'https://example.com/rooms/executive-suite-1.jpg'),
(3, 'https://example.com/rooms/executive-suite-2.jpg'),
(4, 'https://example.com/rooms/ocean-view-1.jpg'),
(4, 'https://example.com/rooms/ocean-view-2.jpg'),
(5, 'https://example.com/rooms/garden-villa-1.jpg'),
(5, 'https://example.com/rooms/garden-villa-2.jpg'),
(6, 'https://example.com/rooms/mountain-cabin-1.jpg'),
(6, 'https://example.com/rooms/mountain-cabin-2.jpg'),
(7, 'https://example.com/rooms/family-room-1.jpg'),
(7, 'https://example.com/rooms/family-room-2.jpg');

-- 12. Insert Addons
INSERT IGNORE INTO addon (addon_name, description, price) VALUES
('Breakfast Buffet', 'Delicious breakfast buffet with local and international dishes', 200000),
('Airport Transfer', 'Convenient airport pickup and drop-off service', 500000),
('Spa Treatment', 'Relaxing spa treatment for ultimate relaxation', 800000),
('Dinner Package', 'Gourmet dinner at hotel restaurant', 600000),
('Laundry Service', 'Professional laundry and dry cleaning service', 150000),
('Room Service', '24/7 in-room dining service', 100000);

-- 13. Insert Sample Bookings
INSERT IGNORE INTO booking (user_id, total_room, booking_amount, booking_status) VALUES
(3, 1, 4000000, 'CONFIRMED'),
(4, 1, 6000000, 'PENDING'),
(3, 1, 3500000, 'COMPLETED');

-- 14. Insert Booking Rooms
INSERT IGNORE INTO booking_room (booking_id, room_id, room_price, checkin_date, checkout_date, num_adults, num_children, status) VALUES
(1, 6, 2000000, '2024-01-15 14:00:00', '2024-01-17 12:00:00', 2, 0, 'CONFIRMED'), -- User 3 - Deluxe Double Room
(2, 13, 2500000, '2024-01-20 14:00:00', '2024-01-22 12:00:00', 2, 0, 'PENDING'), -- User 4 - Ocean View Room
(3, 8, 1750000, '2023-12-25 14:00:00', '2023-12-27 12:00:00', 2, 0, 'COMPLETED'); -- User 3 - Deluxe Double Room (completed)

-- 15. Insert Booking Room Addons
INSERT IGNORE INTO booking_room_addon (booking_room_id, addon_id, quantity, price, subtotal) VALUES
(1, 1, 2, 200000, 400000), -- Breakfast for 2 days
(1, 2, 1, 500000, 500000), -- Airport transfer
(2, 1, 2, 200000, 400000), -- Breakfast for 2 days
(3, 1, 2, 200000, 400000); -- Breakfast for 2 days

-- 16. Insert Payments
INSERT IGNORE INTO payment (booking_id, payment_amount, payment_type, payment_status) VALUES
(1, 4000000, 'CREDIT_CARD', 'COMPLETED'),
(2, 6000000, 'BANK_TRANSFER', 'PENDING'),
(3, 3500000, 'CASH', 'COMPLETED');

-- 17. Insert Reviews
INSERT IGNORE INTO review (user_id, room_class_id, rating, content) VALUES
(3, 1, 5, 'Excellent service and comfortable rooms. Highly recommended!'),
(4, 4, 4, 'Beautiful location and friendly staff. Great experience overall.'),
(3, 6, 5, 'Peaceful mountain retreat with amazing views.');

-- 18. Insert Refresh Tokens (for testing authentication)
INSERT IGNORE INTO refresh_tokens (user_id, token, expires_at, status) VALUES
(1, 'admin-refresh-token-123', DATE_ADD(NOW(), INTERVAL 30 DAY), 'ACTIVE'),
(2, 'staff-refresh-token-456', DATE_ADD(NOW(), INTERVAL 30 DAY), 'ACTIVE'),
(3, 'user-refresh-token-789', DATE_ADD(NOW(), INTERVAL 30 DAY), 'ACTIVE');

-- Commit all changes
COMMIT;

-- Display summary
SELECT 'Data insertion completed successfully!' as message;
SELECT COUNT(*) as total_hotels FROM hotel;
SELECT COUNT(*) as total_rooms FROM room;
SELECT COUNT(*) as total_users FROM user;
SELECT COUNT(*) as total_bookings FROM booking; 