# Hướng dẫn Test API bằng Postman

## 1. <PERSON><PERSON><PERSON> bị dữ liệu

### Chạy script SQL
1. Khởi động ứng dụng Spring Boot
2. <PERSON>ết nối MySQL database
3. Chạy file `sample_data.sql` để insert dữ liệu mẫu

### Thông tin đăng nhập
```
Admin: <EMAIL> / 123456
Staff: <EMAIL> / 123456
User: <EMAIL> / 123456
```

## 2. Test Authentication

### 2.1. Login để lấy JWT Token
```
POST http://localhost:8080/api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "123456"
}
```

**Response:**
```json
{
  "accessToken": "eyJhbGciOiJIUzUxMiJ9...",
  "refreshToken": "admin-refresh-token-123",
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "firstName": "Admin",
    "lastName": "User",
    "roles": ["ADMIN"]
  }
}
```

### 2.2. Refresh <PERSON>ken
```
POST http://localhost:8080/api/auth/refresh
Content-Type: application/json

{
  "refreshToken": "admin-refresh-token-123"
}
```

## 3. Test Admin Dashboard API

### 3.1. Lấy dữ liệu Dashboard
```
GET http://localhost:8080/api/admin/dashboard
Authorization: Bearer {access_token}
```

## 4. Test Admin Booking Management

### 4.1. Lấy danh sách tất cả bookings
```
GET http://localhost:8080/api/admin/bookings?page=0&size=10
Authorization: Bearer {access_token}
```

### 4.2. Lấy chi tiết booking
```
GET http://localhost:8080/api/admin/bookings/1
Authorization: Bearer {access_token}
```

### 4.3. Cập nhật trạng thái booking
```
PUT http://localhost:8080/api/admin/bookings/1/status
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "status": "CONFIRMED",
  "note": "Booking confirmed by admin"
}
```

### 4.4. Xác nhận booking
```
PUT http://localhost:8080/api/admin/bookings/1/confirm
Authorization: Bearer {access_token}
```

### 4.5. Check-in
```
PUT http://localhost:8080/api/admin/bookings/1/check-in
Authorization: Bearer {access_token}
```

### 4.6. Check-out
```
PUT http://localhost:8080/api/admin/bookings/1/check-out
Authorization: Bearer {access_token}
```

### 4.7. Hủy booking
```
PUT http://localhost:8080/api/admin/bookings/1/cancel?reason=Customer request
Authorization: Bearer {access_token}
```

### 4.8. Lấy thống kê booking
```
GET http://localhost:8080/api/admin/bookings/statistics?fromDate=2024-01-01&toDate=2024-01-31
Authorization: Bearer {access_token}
```

### 4.9. Lấy báo cáo doanh thu
```
GET http://localhost:8080/api/admin/bookings/revenue/by-hotel?fromDate=2024-01-01&toDate=2024-01-31
Authorization: Bearer {access_token}
```

### 4.10. Lấy danh sách check-in sắp tới
```
GET http://localhost:8080/api/admin/bookings/upcoming-checkins
Authorization: Bearer {access_token}
```

### 4.11. Đánh dấu no-show
```
POST http://localhost:8080/api/admin/bookings/mark-no-show
Authorization: Bearer {access_token}
```

### 4.12. Export dữ liệu booking
```
GET http://localhost:8080/api/admin/bookings/export?status=CONFIRMED
Authorization: Bearer {access_token}
```

## 5. Test Hotel Management

### 5.1. Lấy danh sách hotels
```
GET http://localhost:8080/api/hotels
Authorization: Bearer {access_token}
```

### 5.2. Lấy chi tiết hotel
```
GET http://localhost:8080/api/hotels/1
Authorization: Bearer {access_token}
```

### 5.3. Tạo hotel mới
```
POST http://localhost:8080/api/hotels
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "hotelName": "New Luxury Hotel",
  "location": "789 Le Loi, District 1, Ho Chi Minh City",
  "description": "Brand new 5-star luxury hotel in the heart of Saigon"
}
```

### 5.4. Cập nhật hotel
```
PUT http://localhost:8080/api/hotels/1
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "hotelName": "Grand Hotel Saigon - Updated",
  "location": "123 Nguyen Hue, District 1, Ho Chi Minh City",
  "description": "Updated description for Grand Hotel Saigon"
}
```

### 5.5. Xóa hotel
```
DELETE http://localhost:8080/api/hotels/1
Authorization: Bearer {access_token}
```

## 6. Test Room Management

### 6.1. Lấy danh sách rooms
```
GET http://localhost:8080/api/rooms
Authorization: Bearer {access_token}
```

### 6.2. Lấy chi tiết room
```
GET http://localhost:8080/api/rooms/1
Authorization: Bearer {access_token}
```

### 6.3. Tạo room mới
```
POST http://localhost:8080/api/rooms
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "roomClassId": 1,
  "roomNumber": "106",
  "roomStatus": "AVAILABLE"
}
```

### 6.4. Cập nhật room
```
PUT http://localhost:8080/api/rooms/1
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "roomNumber": "101",
  "roomStatus": "MAINTENANCE"
}
```

### 6.5. Xóa room
```
DELETE http://localhost:8080/api/rooms/1
Authorization: Bearer {access_token}
```

## 7. Test Room Class Management

### 7.1. Lấy danh sách room classes
```
GET http://localhost:8080/api/room-classes
Authorization: Bearer {access_token}
```

### 7.2. Lấy chi tiết room class
```
GET http://localhost:8080/api/room-classes/1
Authorization: Bearer {access_token}
```

### 7.3. Tạo room class mới
```
POST http://localhost:8080/api/room-classes
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "roomClassName": "Premium Suite",
  "quantity": "5",
  "priceOriginal": 5000000,
  "description": "Ultra luxury suite with premium amenities",
  "discountPercent": 0,
  "capacity": 2,
  "hotelId": 1
}
```

## 8. Test User Management

### 8.1. Lấy danh sách users
```
GET http://localhost:8080/api/users
Authorization: Bearer {access_token}
```

### 8.2. Lấy chi tiết user
```
GET http://localhost:8080/api/users/1
Authorization: Bearer {access_token}
```

### 8.3. Tạo user mới
```
POST http://localhost:8080/api/users
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "123456",
  "firstName": "New",
  "lastName": "User",
  "phoneNo": "0555666777",
  "roles": ["USER"]
}
```

## 9. Test Addon Management

### 9.1. Lấy danh sách addons
```
GET http://localhost:8080/api/addons
Authorization: Bearer {access_token}
```

### 9.2. Tạo addon mới
```
POST http://localhost:8080/api/addons
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "addonName": "Late Checkout",
  "description": "Extended checkout until 2 PM",
  "price": 300000
}
```

## 10. Test Feature Management

### 10.1. Lấy danh sách features
```
GET http://localhost:8080/api/features
Authorization: Bearer {access_token}
```

### 10.2. Tạo feature mới
```
POST http://localhost:8080/api/features
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "featureName": "Swimming Pool",
  "description": "Outdoor swimming pool with sun loungers",
  "iconUrl": "https://example.com/icons/pool.png"
}
```

## 11. Các trạng thái Booking

```
PENDING - Chờ xác nhận
CONFIRMED - Đã xác nhận
CHECKED_IN - Đã check-in
CHECKED_OUT - Đã check-out
COMPLETED - Hoàn thành
CANCELLED - Đã hủy
NO_SHOW - Không đến
```

## 12. Các trạng thái Room

```
AVAILABLE - Có sẵn
OCCUPIED - Đang có khách
MAINTENANCE - Bảo trì
RESERVED - Đã đặt trước
```

## 13. Các trạng thái Payment

```
PENDING - Chờ thanh toán
COMPLETED - Đã thanh toán
FAILED - Thanh toán thất bại
REFUNDED - Đã hoàn tiền
```

## 14. Lưu ý quan trọng

1. **Authentication**: Tất cả API admin đều cần JWT token trong header `Authorization: Bearer {token}`
2. **CORS**: Backend đã cấu hình CORS cho `http://localhost:5173` (React)
3. **Database**: Hiện tại đang dùng `create-drop` mode, dữ liệu sẽ mất khi restart
4. **Security**: Các endpoint admin cần role ADMIN hoặc STAFF
5. **Validation**: Kiểm tra dữ liệu đầu vào trước khi gọi API

## 15. Troubleshooting

### Lỗi 401 Unauthorized
- Kiểm tra JWT token có hợp lệ không
- Kiểm tra token có hết hạn không
- Kiểm tra user có đủ quyền không

### Lỗi 403 Forbidden
- Kiểm tra user có role ADMIN/STAFF không
- Kiểm tra endpoint có yêu cầu quyền đặc biệt không

### Lỗi 500 Internal Server Error
- Kiểm tra database connection
- Kiểm tra log của ứng dụng
- Kiểm tra dữ liệu đầu vào có hợp lệ không 