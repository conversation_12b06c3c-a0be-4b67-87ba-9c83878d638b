# syntax=docker/dockerfile:1

# ---- Build stage ----
FROM maven:3.9.6-eclipse-temurin-21 AS builder
WORKDIR /app

# Cache dependencies
COPY pom.xml .
RUN mvn -q -e -B -DskipTests dependency:go-offline

# Build
COPY src ./src
RUN mvn -q -e -B -DskipTests package

# ---- Runtime stage ----
FROM eclipse-temurin:21-jre
WORKDIR /app

# Copy the fat jar from the build stage
COPY --from=builder /app/target/*.jar /app/app.jar

# You can pass extra JVM options via JAVA_OPTS
ENV JAVA_OPTS=""
EXPOSE 8080

ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -jar /app/app.jar"] 